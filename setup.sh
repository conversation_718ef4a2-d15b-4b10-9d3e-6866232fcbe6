#!/bin/bash

# Pangolin + CrowdSec + <PERSON><PERSON><PERSON><PERSON> Setup Script
# This script sets up the complete stack with user-provided configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to validate domain format
validate_domain() {
    local domain=$1
    if [[ $domain =~ ^[a-zA-Z0-9][a-zA-Z0-9-]{0,61}[a-zA-Z0-9]?\.[a-zA-Z]{2,}$ ]]; then
        return 0
    else
        return 1
    fi
}

# Function to validate email format
validate_email() {
    local email=$1
    if [[ $email =~ ^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$ ]]; then
        return 0
    else
        return 1
    fi
}

# Function to generate random string
generate_secret() {
    openssl rand -base64 32 | tr -d "=+/" | cut -c1-32
}

# Function to prompt for input with validation
prompt_with_validation() {
    local prompt=$1
    local validation_func=$2
    local error_msg=$3
    local value
    
    while true; do
        read -p "$prompt" value
        if $validation_func "$value"; then
            echo "$value"
            return 0
        else
            print_error "$error_msg"
        fi
    done
}

# Function to prompt for password with confirmation
prompt_password() {
    local prompt=$1
    local password
    local confirm_password
    
    while true; do
        read -s -p "$prompt" password
        echo
        read -s -p "Confirm password: " confirm_password
        echo
        
        if [ "$password" = "$confirm_password" ]; then
            if [ ${#password} -lt 8 ]; then
                print_error "Password must be at least 8 characters long"
                continue
            fi
            echo "$password"
            return 0
        else
            print_error "Passwords do not match. Please try again."
        fi
    done
}

print_status "Starting Pangolin + CrowdSec + Traefik setup..."

# Check if Docker and Docker Compose are installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create directory structure
print_status "Creating directory structure..."
mkdir -p config/traefik
mkdir -p config/letsencrypt
print_success "Directory structure created"

# Collect user input
echo
print_status "Please provide the following information:"
echo

# Get domain
DOMAIN=$(prompt_with_validation "Enter your domain (e.g., example.com): " validate_domain "Invalid domain format. Please enter a valid domain.")

# Get email
EMAIL=$(prompt_with_validation "Enter your email address (for Let's Encrypt): " validate_email "Invalid email format. Please enter a valid email address.")

# Get admin password
echo
ADMIN_PASSWORD=$(prompt_password "Enter admin password for Pangolin (min 8 characters): ")

# Generate secret key
SECRET_KEY=$(generate_secret)

print_success "Configuration collected successfully"

# Create config files from templates
print_status "Creating configuration files..."

# Create config.yml
cat > config/config.yml << EOF
app:
    dashboard_url: "https://${DOMAIN}"
    log_level: "info"
    save_logs: false

domains:
    domain1:
        base_domain: "${DOMAIN}"
        cert_resolver: "letsencrypt"

server:
    external_port: 3000
    internal_port: 3001
    next_port: 3002
    internal_hostname: "pangolin"
    session_cookie_name: "p_session_token"
    resource_access_token_param: "p_token"
    resource_access_token_headers:
        id: "P-Access-Token-Id"
        token: "P-Access-Token"
    resource_session_request_param: "p_session_request"
    secret: ${SECRET_KEY}
    cors:
        origins: ["https://${DOMAIN}"]
        methods: ["GET", "POST", "PUT", "DELETE", "PATCH"]
        headers: ["X-CSRF-Token", "Content-Type"]
        credentials: false

traefik:
    cert_resolver: "letsencrypt"
    http_entrypoint: "web"
    https_entrypoint: "websecure"

gerbil:
    start_port: 51820
    base_endpoint: "${DOMAIN}"
    use_subdomain: false
    block_size: 24
    site_block_size: 30
    subnet_group: ************/20

rate_limits:
    global:
        window_minutes: 1
        max_requests: 500

users:
    server_admin:
        email: "admin@${DOMAIN}"
        password: "${ADMIN_PASSWORD}"

flags:
    require_email_verification: false
    disable_signup_without_invite: true
    disable_user_create_org: false
    allow_raw_resources: true
    allow_base_domain_resources: true
EOF

# Create traefik_config.yml
cat > config/traefik/traefik_config.yml << EOF
api:
  insecure: true
  dashboard: true

providers:
  http:
    endpoint: "http://pangolin:3001/api/v1/traefik-config"
    pollInterval: "5s"
  file:
    filename: "/etc/traefik/dynamic_config.yml"

experimental:
  plugins:
    badger:
      moduleName: "github.com/fosrl/badger"
      version: "v1.2.0"

log:
  level: "INFO"
  format: "common"

certificatesResolvers:
  letsencrypt:
    acme:
      httpChallenge:
        entryPoint: web
      email: ${EMAIL}
      storage: "/letsencrypt/acme.json"
      caServer: "https://acme-v02.api.letsencrypt.org/directory"

entryPoints:
  web:
    address: ":80"
  websecure:
    address: ":443"
    transport:
      respondingTimeouts:
        readTimeout: "30m"
    http:
      tls:
        certResolver: "letsencrypt"

serversTransport:
  insecureSkipVerify: true
EOF

# Create dynamic_config.yml
cat > config/traefik/dynamic_config.yml << EOF
http:
  middlewares:
    redirect-to-https:
      redirectScheme:
        scheme: https

  routers:
    # HTTP to HTTPS redirect router
    main-app-router-redirect:
      rule: "Host(\`${DOMAIN}\`)"
      service: next-service
      entryPoints:
        - web
      middlewares:
        - redirect-to-https

    # Next.js router (handles everything except API and WebSocket paths)
    next-router:
      rule: "Host(\`${DOMAIN}\`) && !PathPrefix(\`/api/v1\`)"
      service: next-service
      entryPoints:
        - websecure
      tls:
        certResolver: letsencrypt

    # API router (handles /api/v1 paths)
    api-router:
      rule: "Host(\`${DOMAIN}\`) && PathPrefix(\`/api/v1\`)"
      service: api-service
      entryPoints:
        - websecure
      tls:
        certResolver: letsencrypt

    # WebSocket router
    ws-router:
      rule: "Host(\`${DOMAIN}\`)"
      service: api-service
      entryPoints:
        - websecure
      tls:
        certResolver: letsencrypt

  services:
    next-service:
      loadBalancer:
        servers:
          - url: "http://pangolin:3002" # Next.js server

    api-service:
      loadBalancer:
        servers:
          - url: "http://pangolin:3000" # API/WebSocket server
EOF

print_success "Configuration files created"

# Start the stack
print_status "Starting Docker Compose stack..."
docker-compose up -d

print_success "Stack started successfully!"

echo
print_success "Setup completed! Your Pangolin instance is now running."
echo
print_status "Access your dashboard at: https://${DOMAIN}"
print_status "Admin login: admin@${DOMAIN}"
print_status "Admin password: ${ADMIN_PASSWORD}"
echo
print_warning "Make sure your domain ${DOMAIN} points to this server's IP address"
print_warning "It may take a few minutes for Let's Encrypt certificates to be issued"
echo
print_status "To view logs: docker-compose logs -f"
print_status "To stop: docker-compose down"
print_status "To restart: docker-compose restart"
