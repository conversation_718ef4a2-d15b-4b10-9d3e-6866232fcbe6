# Pangolin + CrowdSec + Traefik Stack

A sleek, one-command deployment of Pangolin with CrowdSec security and <PERSON><PERSON><PERSON><PERSON> reverse proxy.

## Features

- 🚀 **One-line installation** - Deploy everything with a single command
- 🔒 **Automatic HTTPS** - Let's Encrypt certificates managed by <PERSON><PERSON><PERSON><PERSON>
- 🛡️ **Security** - CrowdSec integration for threat protection
- 🎯 **Interactive setup** - Prompts for domain, email, and admin password
- 📁 **Auto-configuration** - Creates all required files and folder structure
- 🔐 **Secure secrets** - Auto-generates secure random keys

## Quick Start

### Prerequisites

- Linux server with Docker installed
- Domain name pointing to your server's IP address
- Ports 80, 443, and 51820 available

### One-Line Installation

```bash
curl -sSL https://raw.githubusercontent.com/yourusername/pangolin-crowdsec-stack/main/install.sh | bash
```

### What happens during installation:

1. **Validation** - Checks Docker installation and permissions
2. **Download** - Fetches setup files from GitHub
3. **Interactive setup** - Prompts for:
   - Your domain name
   - Email address (for Let's Encrypt)
   - Admin password for Pangolin
4. **Configuration** - Creates all config files with your settings
5. **Deployment** - Starts the Docker Compose stack

## Manual Installation

If you prefer to review files before running:

```bash
# Clone or download the repository
git clone https://github.com/yourusername/pangolin-crowdsec-stack.git
cd pangolin-crowdsec-stack

# Run the setup script
./setup.sh
```

## Stack Components

- **Pangolin** (fosrl/pangolin:1.5.0) - Main application
- **Gerbil** (fosrl/gerbil:1.0.0) - WireGuard VPN management
- **Traefik** (traefik:v3.4.0) - Reverse proxy with automatic HTTPS

## Directory Structure

After installation, you'll have:

```
pangolin-stack/
├── docker-compose.yml
├── setup.sh
└── config/
    ├── config.yml
    ├── letsencrypt/          # Let's Encrypt certificates
    └── traefik/
        ├── traefik_config.yml
        └── dynamic_config.yml
```

## Management Commands

```bash
# View logs
docker-compose logs -f

# Restart services
docker-compose restart

# Stop services
docker-compose down

# Start services
docker-compose up -d

# Update images
docker-compose pull
docker-compose up -d
```

## Configuration

All configuration is automatically generated during setup. Key files:

- `config/config.yml` - Main Pangolin configuration
- `config/traefik/traefik_config.yml` - Traefik main config
- `config/traefik/dynamic_config.yml` - Traefik routing rules

## Accessing Your Installation

After successful deployment:

- **Dashboard**: `https://yourdomain.com`
- **Admin Login**: `<EMAIL>`
- **Password**: The password you set during installation

## Troubleshooting

### Common Issues

1. **Domain not resolving**
   - Ensure your domain's DNS A record points to your server's IP
   - Wait for DNS propagation (can take up to 24 hours)

2. **Certificate issues**
   - Let's Encrypt certificates can take a few minutes to issue
   - Check logs: `docker-compose logs traefik`

3. **Permission errors**
   - Ensure your user is in the docker group: `sudo usermod -aG docker $USER`
   - Log out and back in after adding to docker group

### Viewing Logs

```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f pangolin
docker-compose logs -f traefik
docker-compose logs -f gerbil
```

## Security Notes

- The setup generates secure random secrets automatically
- Admin password is set during installation
- All traffic is automatically redirected to HTTPS
- CrowdSec provides additional security monitoring

## Support

For issues and questions:
- Check the logs first: `docker-compose logs -f`
- Ensure your domain DNS is properly configured
- Verify all ports (80, 443, 51820) are accessible

## License

This deployment stack is provided as-is. Please refer to individual component licenses:
- Pangolin: Check fosrl/pangolin repository
- Gerbil: Check fosrl/gerbil repository  
- Traefik: Apache 2.0 License
