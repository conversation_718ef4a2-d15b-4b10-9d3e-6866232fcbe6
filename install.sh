#!/bin/bash

# One-liner installer for Pangolin + CrowdSec + Traefik
# This script downloads the setup files and runs the installation

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration - Update these URLs to match your GitHub repository
GITHUB_USER="yourusername"
GITHUB_REPO="pangolin-crowdsec-stack"
GITHUB_BRANCH="main"
BASE_URL="https://raw.githubusercontent.com/${GITHUB_USER}/${GITHUB_REPO}/${GITHUB_BRANCH}"

print_status "Pangolin + CrowdSec + Traefik One-Line Installer"
echo

# Check if we're running as root (not recommended for Docker)
if [ "$EUID" -eq 0 ]; then
    print_error "Please don't run this script as root. Run as a regular user with Docker permissions."
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first:"
    echo "  curl -fsSL https://get.docker.com | sh"
    echo "  sudo usermod -aG docker \$USER"
    echo "  # Log out and back in, then run this script again"
    exit 1
fi

# Check if user is in docker group
if ! groups | grep -q docker; then
    print_error "Current user is not in the docker group. Please add yourself to the docker group:"
    echo "  sudo usermod -aG docker \$USER"
    echo "  # Log out and back in, then run this script again"
    exit 1
fi

# Create installation directory
INSTALL_DIR="pangolin-stack"
if [ -d "$INSTALL_DIR" ]; then
    print_error "Directory $INSTALL_DIR already exists. Please remove it or choose a different location."
    exit 1
fi

print_status "Creating installation directory: $INSTALL_DIR"
mkdir -p "$INSTALL_DIR"
cd "$INSTALL_DIR"

# Download required files
print_status "Downloading setup files..."

# Download docker-compose.yml
if ! curl -fsSL "${BASE_URL}/docker-compose.yml" -o docker-compose.yml; then
    print_error "Failed to download docker-compose.yml"
    exit 1
fi

# Download setup script
if ! curl -fsSL "${BASE_URL}/setup.sh" -o setup.sh; then
    print_error "Failed to download setup.sh"
    exit 1
fi

# Make setup script executable
chmod +x setup.sh

print_success "Files downloaded successfully"

# Run the setup script
print_status "Running setup script..."
./setup.sh

print_success "Installation completed!"
echo
print_status "Your Pangolin stack is now running in the directory: $(pwd)"
print_status "To manage your stack:"
echo "  cd $(pwd)"
echo "  docker-compose logs -f    # View logs"
echo "  docker-compose restart    # Restart services"
echo "  docker-compose down       # Stop services"
